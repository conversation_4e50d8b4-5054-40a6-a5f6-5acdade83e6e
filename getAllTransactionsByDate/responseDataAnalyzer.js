#!/usr/bin/env node

/**
 * Response Data Analyzer Script
 * 
 * This script analyzes transaction data from responseData.json format and creates summary objects
 * showing platform-wise transaction amounts and status distributions.
 * 
 * Usage: node responseDataAnalyzer.js <path-to-responseData.json>
 * 
 * Expected format: { "data": { "jaql": { "PLATFORM_NAME": [...transactions] } } }
 */

const fs = require('fs');
const path = require('path');

/**
 * Analyzes responseData.json transaction data
 * @param {string} jsonFilePath - Path to the responseData.json file
 * @returns {Object} Analysis results containing platform summaries and status summaries
 */
function analyzeResponseData(jsonFilePath) {
    try {
        console.log('📊 Starting responseData.json analysis...');
        console.log(`📁 File: ${jsonFilePath}`);
        
        // Read and parse JSON file
        const jsonData = fs.readFileSync(jsonFilePath, 'utf8');
        const data = JSON.parse(jsonData);
        
        // Validate format
        if (!data.data || !data.data.jaql) {
            throw new Error('Invalid responseData.json format. Expected { data: { jaql: {...} } }');
        }
        
        const jaqlData = data.data.jaql;
        const platforms = Object.keys(jaqlData);
        
        console.log(`🏢 Platforms found: ${platforms.join(', ')}`);
        
        // Create analysis objects
        const platformSummary = createPlatformSummary(jaqlData);
        const statusSummary = createStatusSummary(jaqlData);
        
        // Calculate total transactions across all platforms
        const totalTransactions = platforms.reduce((total, platform) => {
            return total + (Array.isArray(jaqlData[platform]) ? jaqlData[platform].length : 0);
        }, 0);
        
        console.log(`📈 Total transactions found: ${totalTransactions}`);
        
        return {
            platformSummary,
            statusSummary,
            totalTransactions,
            platforms,
            analysisDate: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ Error analyzing responseData:', error.message);
        throw error;
    }
}

/**
 * Creates platform-wise summary with transaction counts and amounts
 * @param {Object} jaqlData - The jaql data object containing platform arrays
 * @returns {Object} Platform summary object with jaql and sql views
 */
function createPlatformSummary(jaqlData) {
    const summary = {
        jaql: {},
        sql: {}
    };
    
    Object.keys(jaqlData).forEach(platformName => {
        const transactions = jaqlData[platformName];
        
        if (!Array.isArray(transactions)) {
            console.warn(`⚠️  Platform ${platformName} does not contain an array of transactions`);
            return;
        }
        
        // Initialize platform summary
        const platformStats = {
            totalTransactions: transactions.length,
            totalAmount: 0,
            successfulTransactions: 0,
            successfulAmount: 0,
            billableTransactions: 0,
            billableAmount: 0,
            currency: transactions.length > 0 ? transactions[0].currency : 'UNKNOWN',
            averageAmount: 0,
            statusBreakdown: {}
        };
        
        // Process each transaction
        transactions.forEach(tx => {
            const amount = parseFloat(tx.finalAmt || 0);
            platformStats.totalAmount += amount;
            
            // Count successful transactions
            if (tx.status === 'STATUS_SUCCESS') {
                platformStats.successfulTransactions++;
                platformStats.successfulAmount += amount;
            }
            
            // Count billable transactions
            if (tx.billable === true) {
                platformStats.billableTransactions++;
                platformStats.billableAmount += amount;
            }
            
            // Status breakdown
            if (!platformStats.statusBreakdown[tx.status]) {
                platformStats.statusBreakdown[tx.status] = {
                    count: 0,
                    amount: 0
                };
            }
            platformStats.statusBreakdown[tx.status].count++;
            platformStats.statusBreakdown[tx.status].amount += amount;
        });
        
        // Calculate average
        platformStats.averageAmount = platformStats.totalTransactions > 0 
            ? platformStats.totalAmount / platformStats.totalTransactions 
            : 0;
        
        // Add to both jaql and sql views (same data, different perspectives)
        summary.jaql[platformName] = { ...platformStats };
        summary.sql[platformName] = { ...platformStats };
    });
    
    return summary;
}

/**
 * Creates status-wise summary with counts per platform
 * @param {Object} jaqlData - The jaql data object containing platform arrays
 * @returns {Object} Status summary object with jaql and sql views
 */
function createStatusSummary(jaqlData) {
    const summary = {
        jaql: {},
        sql: {}
    };
    
    Object.keys(jaqlData).forEach(platformName => {
        const transactions = jaqlData[platformName];
        
        if (!Array.isArray(transactions)) return;
        
        // Initialize platform status summary
        const platformStatusSummary = {};
        
        transactions.forEach(tx => {
            const status = tx.status || 'UNKNOWN';
            const amount = parseFloat(tx.finalAmt || 0);
            
            if (!platformStatusSummary[status]) {
                platformStatusSummary[status] = {
                    count: 0,
                    totalAmount: 0,
                    billableCount: 0,
                    billableAmount: 0
                };
            }
            
            platformStatusSummary[status].count++;
            platformStatusSummary[status].totalAmount += amount;
            
            if (tx.billable === true) {
                platformStatusSummary[status].billableCount++;
                platformStatusSummary[status].billableAmount += amount;
            }
        });
        
        // Add to both jaql and sql views
        summary.jaql[platformName] = { ...platformStatusSummary };
        summary.sql[platformName] = { ...platformStatusSummary };
    });
    
    return summary;
}

/**
 * Displays analysis results in a formatted way
 * @param {Object} results - Analysis results
 */
function displayResults(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📊 RESPONSE DATA ANALYSIS RESULTS');
    console.log('='.repeat(80));
    
    console.log(`\n📈 Total Transactions: ${results.totalTransactions}`);
    console.log(`🏢 Platforms: ${results.platforms.join(', ')}`);
    console.log(`📅 Analysis Date: ${results.analysisDate}`);
    
    // Display Platform Summary
    console.log('\n' + '─'.repeat(60));
    console.log('🏢 PLATFORM SUMMARY (JAQL & SQL Views)');
    console.log('─'.repeat(60));
    
    results.platforms.forEach(platform => {
        const data = results.platformSummary.jaql[platform];
        if (!data) return;
        
        console.log(`\n🔹 Platform: ${platform}`);
        console.log(`   Total Transactions: ${data.totalTransactions}`);
        console.log(`   Total Amount: ${data.totalAmount.toFixed(2)} ${data.currency}`);
        console.log(`   Average Amount: ${data.averageAmount.toFixed(2)} ${data.currency}`);
        console.log(`   Successful: ${data.successfulTransactions} (${data.successfulAmount.toFixed(2)} ${data.currency})`);
        console.log(`   Billable: ${data.billableTransactions} (${data.billableAmount.toFixed(2)} ${data.currency})`);
        console.log(`   Success Rate: ${((data.successfulTransactions / data.totalTransactions) * 100).toFixed(1)}%`);
        console.log(`   Billable Rate: ${((data.billableTransactions / data.totalTransactions) * 100).toFixed(1)}%`);
    });
    
    // Display Status Summary
    console.log('\n' + '─'.repeat(60));
    console.log('📊 STATUS SUMMARY BY PLATFORM');
    console.log('─'.repeat(60));
    
    results.platforms.forEach(platform => {
        const statuses = results.statusSummary.jaql[platform];
        if (!statuses) return;
        
        console.log(`\n🔹 Platform: ${platform}`);
        Object.keys(statuses).forEach(status => {
            const statusData = statuses[status];
            console.log(`   ${status}:`);
            console.log(`     Total: ${statusData.count} transactions, ${statusData.totalAmount.toFixed(2)} amount`);
            console.log(`     Billable: ${statusData.billableCount} transactions, ${statusData.billableAmount.toFixed(2)} amount`);
        });
    });
    
    // Display Overall Summary
    console.log('\n' + '─'.repeat(60));
    console.log('📈 OVERALL SUMMARY');
    console.log('─'.repeat(60));
    
    const overallStats = calculateOverallStats(results);
    console.log(`\n💰 Grand Total Amount: ${overallStats.grandTotal.toFixed(2)} ${overallStats.currency}`);
    console.log(`✅ Total Successful: ${overallStats.totalSuccessful} transactions (${overallStats.successfulAmount.toFixed(2)} ${overallStats.currency})`);
    console.log(`💳 Total Billable: ${overallStats.totalBillable} transactions (${overallStats.billableAmount.toFixed(2)} ${overallStats.currency})`);
    console.log(`📊 Overall Success Rate: ${overallStats.successRate.toFixed(1)}%`);
    console.log(`📊 Overall Billable Rate: ${overallStats.billableRate.toFixed(1)}%`);
}

/**
 * Calculates overall statistics across all platforms
 * @param {Object} results - Analysis results
 * @returns {Object} Overall statistics
 */
function calculateOverallStats(results) {
    let grandTotal = 0;
    let totalSuccessful = 0;
    let successfulAmount = 0;
    let totalBillable = 0;
    let billableAmount = 0;
    let currency = 'CAD';
    
    results.platforms.forEach(platform => {
        const data = results.platformSummary.jaql[platform];
        if (data) {
            grandTotal += data.totalAmount;
            totalSuccessful += data.successfulTransactions;
            successfulAmount += data.successfulAmount;
            totalBillable += data.billableTransactions;
            billableAmount += data.billableAmount;
            currency = data.currency;
        }
    });
    
    return {
        grandTotal,
        totalSuccessful,
        successfulAmount,
        totalBillable,
        billableAmount,
        currency,
        successRate: results.totalTransactions > 0 ? (totalSuccessful / results.totalTransactions) * 100 : 0,
        billableRate: results.totalTransactions > 0 ? (totalBillable / results.totalTransactions) * 100 : 0
    };
}

/**
 * Saves analysis results to JSON files
 * @param {Object} results - Analysis results
 * @param {string} outputDir - Output directory path
 */
function saveResults(results, outputDir = './analysis_output') {
    try {
        // Create output directory if it doesn't exist
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        // Save complete results
        const resultsFile = path.join(outputDir, `responseData_analysis_${timestamp}.json`);
        fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
        
        // Save platform summary separately
        const platformFile = path.join(outputDir, `platform_summary_${timestamp}.json`);
        fs.writeFileSync(platformFile, JSON.stringify(results.platformSummary, null, 2));
        
        // Save status summary separately
        const statusFile = path.join(outputDir, `status_summary_${timestamp}.json`);
        fs.writeFileSync(statusFile, JSON.stringify(results.statusSummary, null, 2));
        
        console.log(`\n💾 Results saved to:`);
        console.log(`   📄 Complete analysis: ${resultsFile}`);
        console.log(`   📄 Platform summary: ${platformFile}`);
        console.log(`   📄 Status summary: ${statusFile}`);
        
    } catch (error) {
        console.error('❌ Error saving results:', error.message);
    }
}

// Main execution
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('❌ Usage: node responseDataAnalyzer.js <path-to-responseData.json>');
        console.log('\nExample:');
        console.log('  node responseDataAnalyzer.js ./getAllTransactionsByDate/responseData.json');
        process.exit(1);
    }
    
    const jsonFilePath = args[0];
    
    if (!fs.existsSync(jsonFilePath)) {
        console.error(`❌ File not found: ${jsonFilePath}`);
        process.exit(1);
    }
    
    try {
        const results = analyzeResponseData(jsonFilePath);
        displayResults(results);
        saveResults(results);
        
        console.log('\n✅ Analysis completed successfully!');
        
    } catch (error) {
        console.error('❌ Analysis failed:', error.message);
        process.exit(1);
    }
}

// Export functions for testing
module.exports = {
    analyzeResponseData,
    createPlatformSummary,
    createStatusSummary,
    calculateOverallStats
};

// Run main function if script is executed directly
if (require.main === module) {
    main();
}
