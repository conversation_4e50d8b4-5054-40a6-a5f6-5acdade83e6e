const { 
    analyzeResponseData, 
    createPlatformSummary, 
    createStatusSummary,
    calculateOverallStats
} = require('./getAllTransactionsByDate/responseDataAnalyzer');

describe('Response Data Analyzer', () => {
    // Sample responseData.json format for testing
    const sampleResponseData = {
        "data": {
            "jaql": {
                "ETI": [
                    {
                        "finalAmt": 50,
                        "status": "STATUS_SUCCESS",
                        "currency": "CAD",
                        "billable": true
                    },
                    {
                        "finalAmt": 75,
                        "status": "STATUS_EXPIRED",
                        "currency": "CAD",
                        "billable": false
                    }
                ],
                "RFM": [
                    {
                        "finalAmt": 100,
                        "status": "STATUS_SUCCESS",
                        "currency": "CAD",
                        "billable": true
                    },
                    {
                        "finalAmt": 200,
                        "status": "STATUS_REJECTED",
                        "currency": "CAD",
                        "billable": false
                    }
                ]
            }
        }
    };

    describe('createPlatformSummary', () => {
        test('should create correct platform summary for responseData format', () => {
            const summary = createPlatformSummary(sampleResponseData.data.jaql);
            
            // Check ETI platform
            expect(summary.jaql.ETI).toMatchObject({
                totalTransactions: 2,
                totalAmount: 125,
                successfulTransactions: 1,
                successfulAmount: 50,
                billableTransactions: 1,
                billableAmount: 50,
                currency: 'CAD'
            });
            
            // Check RFM platform
            expect(summary.jaql.RFM).toMatchObject({
                totalTransactions: 2,
                totalAmount: 300,
                successfulTransactions: 1,
                successfulAmount: 100,
                billableTransactions: 1,
                billableAmount: 100,
                currency: 'CAD'
            });
            
            // Check that sql view is identical to jaql view
            expect(summary.sql).toEqual(summary.jaql);
        });

        test('should calculate average amounts correctly', () => {
            const summary = createPlatformSummary(sampleResponseData.data.jaql);
            
            expect(summary.jaql.ETI.averageAmount).toBe(62.5); // (50 + 75) / 2
            expect(summary.jaql.RFM.averageAmount).toBe(150); // (100 + 200) / 2
        });

        test('should handle status breakdown correctly', () => {
            const summary = createPlatformSummary(sampleResponseData.data.jaql);
            
            // Check ETI status breakdown
            expect(summary.jaql.ETI.statusBreakdown.STATUS_SUCCESS).toMatchObject({
                count: 1,
                amount: 50
            });
            expect(summary.jaql.ETI.statusBreakdown.STATUS_EXPIRED).toMatchObject({
                count: 1,
                amount: 75
            });
            
            // Check RFM status breakdown
            expect(summary.jaql.RFM.statusBreakdown.STATUS_SUCCESS).toMatchObject({
                count: 1,
                amount: 100
            });
            expect(summary.jaql.RFM.statusBreakdown.STATUS_REJECTED).toMatchObject({
                count: 1,
                amount: 200
            });
        });
    });

    describe('createStatusSummary', () => {
        test('should create correct status summary', () => {
            const summary = createStatusSummary(sampleResponseData.data.jaql);
            
            // Check ETI statuses
            expect(summary.jaql.ETI.STATUS_SUCCESS).toMatchObject({
                count: 1,
                totalAmount: 50,
                billableCount: 1,
                billableAmount: 50
            });
            
            expect(summary.jaql.ETI.STATUS_EXPIRED).toMatchObject({
                count: 1,
                totalAmount: 75,
                billableCount: 0,
                billableAmount: 0
            });
            
            // Check RFM statuses
            expect(summary.jaql.RFM.STATUS_SUCCESS).toMatchObject({
                count: 1,
                totalAmount: 100,
                billableCount: 1,
                billableAmount: 100
            });
            
            expect(summary.jaql.RFM.STATUS_REJECTED).toMatchObject({
                count: 1,
                totalAmount: 200,
                billableCount: 0,
                billableAmount: 0
            });
            
            // Check that sql view is identical to jaql view
            expect(summary.sql).toEqual(summary.jaql);
        });
    });

    describe('calculateOverallStats', () => {
        test('should calculate correct overall statistics', () => {
            const platformSummary = createPlatformSummary(sampleResponseData.data.jaql);
            const results = {
                platformSummary,
                platforms: ['ETI', 'RFM'],
                totalTransactions: 4
            };
            
            const overallStats = calculateOverallStats(results);
            
            expect(overallStats).toMatchObject({
                grandTotal: 425, // 125 + 300
                totalSuccessful: 2, // 1 + 1
                successfulAmount: 150, // 50 + 100
                totalBillable: 2, // 1 + 1
                billableAmount: 150, // 50 + 100
                currency: 'CAD',
                successRate: 50, // 2/4 * 100
                billableRate: 50 // 2/4 * 100
            });
        });
    });

    describe('Edge cases', () => {
        test('should handle empty platforms gracefully', () => {
            const emptyData = {
                "ETI": [],
                "RFM": []
            };
            
            const platformSummary = createPlatformSummary(emptyData);
            const statusSummary = createStatusSummary(emptyData);
            
            expect(platformSummary.jaql.ETI.totalTransactions).toBe(0);
            expect(platformSummary.jaql.RFM.totalTransactions).toBe(0);
            expect(Object.keys(statusSummary.jaql.ETI)).toHaveLength(0);
            expect(Object.keys(statusSummary.jaql.RFM)).toHaveLength(0);
        });

        test('should handle missing amounts gracefully', () => {
            const dataWithMissingAmounts = {
                "ETI": [
                    {
                        "status": "STATUS_SUCCESS",
                        "currency": "CAD",
                        "billable": true
                        // finalAmt is missing
                    }
                ]
            };
            
            const summary = createPlatformSummary(dataWithMissingAmounts);
            expect(summary.jaql.ETI.totalAmount).toBe(0);
            expect(summary.jaql.ETI.averageAmount).toBe(0);
        });

        test('should handle non-array platform data', () => {
            const invalidData = {
                "ETI": "not an array",
                "RFM": [
                    {
                        "finalAmt": 100,
                        "status": "STATUS_SUCCESS",
                        "currency": "CAD",
                        "billable": true
                    }
                ]
            };
            
            const summary = createPlatformSummary(invalidData);
            
            // ETI should not be in the summary due to invalid format
            expect(summary.jaql.ETI).toBeUndefined();
            
            // RFM should be processed normally
            expect(summary.jaql.RFM).toMatchObject({
                totalTransactions: 1,
                totalAmount: 100
            });
        });

        test('should handle unknown status values', () => {
            const dataWithUnknownStatus = {
                "ETI": [
                    {
                        "finalAmt": 50,
                        "currency": "CAD",
                        "billable": true
                        // status is missing
                    }
                ]
            };
            
            const statusSummary = createStatusSummary(dataWithUnknownStatus);
            expect(statusSummary.jaql.ETI.UNKNOWN).toMatchObject({
                count: 1,
                totalAmount: 50
            });
        });
    });

    describe('Real-world scenarios', () => {
        test('should handle multiple platforms with various statuses', () => {
            const complexData = {
                "ETI": [
                    { "finalAmt": 100, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 50, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 75, "status": "STATUS_EXPIRED", "currency": "CAD", "billable": false }
                ],
                "RFM": [
                    { "finalAmt": 200, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 150, "status": "STATUS_REJECTED", "currency": "CAD", "billable": false }
                ],
                "ACH": [
                    { "finalAmt": 1000, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true }
                ]
            };

            const platformSummary = createPlatformSummary(complexData);
            const statusSummary = createStatusSummary(complexData);

            // Verify ETI calculations
            expect(platformSummary.jaql.ETI.totalTransactions).toBe(3);
            expect(platformSummary.jaql.ETI.successfulTransactions).toBe(2);
            expect(platformSummary.jaql.ETI.billableTransactions).toBe(2);

            // Verify status counts
            expect(statusSummary.jaql.ETI.STATUS_SUCCESS.count).toBe(2);
            expect(statusSummary.jaql.ETI.STATUS_EXPIRED.count).toBe(1);

            // Verify ACH (single transaction platform)
            expect(platformSummary.jaql.ACH.totalTransactions).toBe(1);
            expect(platformSummary.jaql.ACH.successfulTransactions).toBe(1);
            expect(platformSummary.jaql.ACH.totalAmount).toBe(1000);
        });
    });

    describe('JAQL vs SQL Data Consistency', () => {
        test('should ensure JAQL and SQL platform summaries have identical data', () => {
            const testData = {
                "ETI": [
                    { "finalAmt": 100, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 50, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 75, "status": "STATUS_EXPIRED", "currency": "CAD", "billable": false },
                    { "finalAmt": 25, "status": "STATUS_REJECTED", "currency": "CAD", "billable": false }
                ],
                "RFM": [
                    { "finalAmt": 200, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 150, "status": "STATUS_REJECTED", "currency": "CAD", "billable": false },
                    { "finalAmt": 300, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true }
                ],
                "ACH": [
                    { "finalAmt": 1000, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 500, "status": "STATUS_EXPIRED", "currency": "CAD", "billable": false }
                ]
            };

            const platformSummary = createPlatformSummary(testData);

            // Test that JAQL and SQL views are completely identical
            expect(platformSummary.sql).toEqual(platformSummary.jaql);

            // Test specific numerical values for each platform
            Object.keys(testData).forEach(platform => {
                const jaqlData = platformSummary.jaql[platform];
                const sqlData = platformSummary.sql[platform];

                // Verify all numerical fields match exactly
                expect(sqlData.totalTransactions).toBe(jaqlData.totalTransactions);
                expect(sqlData.totalAmount).toBe(jaqlData.totalAmount);
                expect(sqlData.successfulTransactions).toBe(jaqlData.successfulTransactions);
                expect(sqlData.successfulAmount).toBe(jaqlData.successfulAmount);
                expect(sqlData.billableTransactions).toBe(jaqlData.billableTransactions);
                expect(sqlData.billableAmount).toBe(jaqlData.billableAmount);
                expect(sqlData.averageAmount).toBe(jaqlData.averageAmount);
                expect(sqlData.currency).toBe(jaqlData.currency);

                // Verify status breakdown is identical
                expect(sqlData.statusBreakdown).toEqual(jaqlData.statusBreakdown);

                // Verify status breakdown numerical values
                Object.keys(jaqlData.statusBreakdown).forEach(status => {
                    expect(sqlData.statusBreakdown[status].count).toBe(jaqlData.statusBreakdown[status].count);
                    expect(sqlData.statusBreakdown[status].amount).toBe(jaqlData.statusBreakdown[status].amount);
                });
            });
        });

        test('should ensure JAQL and SQL status summaries have identical data', () => {
            const testData = {
                "ETI": [
                    { "finalAmt": 100, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 75, "status": "STATUS_EXPIRED", "currency": "CAD", "billable": false },
                    { "finalAmt": 50, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true }
                ],
                "RFM": [
                    { "finalAmt": 200, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true },
                    { "finalAmt": 150, "status": "STATUS_REJECTED", "currency": "CAD", "billable": false },
                    { "finalAmt": 100, "status": "STATUS_REJECTED", "currency": "CAD", "billable": false }
                ]
            };

            const statusSummary = createStatusSummary(testData);

            // Test that JAQL and SQL views are completely identical
            expect(statusSummary.sql).toEqual(statusSummary.jaql);

            // Test specific numerical values for each platform and status
            Object.keys(testData).forEach(platform => {
                const jaqlStatuses = statusSummary.jaql[platform];
                const sqlStatuses = statusSummary.sql[platform];

                // Verify all status objects are identical
                expect(sqlStatuses).toEqual(jaqlStatuses);

                // Verify each status has matching numerical values
                Object.keys(jaqlStatuses).forEach(status => {
                    expect(sqlStatuses[status].count).toBe(jaqlStatuses[status].count);
                    expect(sqlStatuses[status].totalAmount).toBe(jaqlStatuses[status].totalAmount);
                    expect(sqlStatuses[status].billableCount).toBe(jaqlStatuses[status].billableCount);
                    expect(sqlStatuses[status].billableAmount).toBe(jaqlStatuses[status].billableAmount);
                });
            });
        });

        test('should maintain consistency with sample responseData format', () => {
            const platformSummary = createPlatformSummary(sampleResponseData.data.jaql);
            const statusSummary = createStatusSummary(sampleResponseData.data.jaql);

            // Verify platform summary consistency
            expect(platformSummary.sql).toEqual(platformSummary.jaql);

            // Verify status summary consistency
            expect(statusSummary.sql).toEqual(statusSummary.jaql);

            // Spot check specific values to ensure they're identical
            expect(platformSummary.sql.ETI.totalAmount).toBe(platformSummary.jaql.ETI.totalAmount);
            expect(platformSummary.sql.RFM.successfulTransactions).toBe(platformSummary.jaql.RFM.successfulTransactions);
            expect(statusSummary.sql.ETI.STATUS_SUCCESS.count).toBe(statusSummary.jaql.ETI.STATUS_SUCCESS.count);
            expect(statusSummary.sql.RFM.STATUS_REJECTED.totalAmount).toBe(statusSummary.jaql.RFM.STATUS_REJECTED.totalAmount);
        });

        test('should handle edge cases with identical JAQL and SQL outputs', () => {
            // Test with empty data
            const emptyData = { "ETI": [], "RFM": [] };
            const emptyPlatformSummary = createPlatformSummary(emptyData);
            const emptyStatusSummary = createStatusSummary(emptyData);

            expect(emptyPlatformSummary.sql).toEqual(emptyPlatformSummary.jaql);
            expect(emptyStatusSummary.sql).toEqual(emptyStatusSummary.jaql);

            // Test with single transaction
            const singleData = {
                "ETI": [{ "finalAmt": 123.45, "status": "STATUS_SUCCESS", "currency": "CAD", "billable": true }]
            };
            const singlePlatformSummary = createPlatformSummary(singleData);
            const singleStatusSummary = createStatusSummary(singleData);

            expect(singlePlatformSummary.sql).toEqual(singlePlatformSummary.jaql);
            expect(singleStatusSummary.sql).toEqual(singleStatusSummary.jaql);

            // Verify the single transaction values are identical
            expect(singlePlatformSummary.sql.ETI.totalAmount).toBe(123.45);
            expect(singlePlatformSummary.jaql.ETI.totalAmount).toBe(123.45);
            expect(singleStatusSummary.sql.ETI.STATUS_SUCCESS.totalAmount).toBe(123.45);
            expect(singleStatusSummary.jaql.ETI.STATUS_SUCCESS.totalAmount).toBe(123.45);
        });
    });
});
